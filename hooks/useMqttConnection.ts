import { useEffect, useState, useCallback } from 'react';
import MyMqttClient from '@/utils/mqtt';
import { router } from 'expo-router';

export interface MqttConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  reconnectAttempts: number;
}

export const useMqttConnection = () => {
  const [connectionState, setConnectionState] = useState<MqttConnectionState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    reconnectAttempts: 0,
  });

  const client = MyMqttClient.getInstance();

  // 检查连接状态
  const checkConnectionStatus = useCallback(() => {
    const isConnected = client.isConnected();
    setConnectionState(prev => ({
      ...prev,
      isConnected,
      error: isConnected ? null : prev.error,
    }));
    return isConnected;
  }, [client]);

  // 手动重连
  const reconnect = useCallback(async () => {
    try {
      setConnectionState(prev => ({
        ...prev,
        isConnecting: true,
        error: null,
      }));

      await client.manualReconnect();
      
      setConnectionState(prev => ({
        ...prev,
        isConnected: true,
        isConnecting: false,
        error: null,
        reconnectAttempts: 0,
      }));

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '重连失败';
      setConnectionState(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false,
        error: errorMessage,
        reconnectAttempts: prev.reconnectAttempts + 1,
      }));
      throw error;
    }
  }, [client]);

  // 监听连接状态变化
  useEffect(() => {
    let statusCheckInterval: NodeJS.Timeout;

    const setupConnectionMonitoring = () => {
      // 初始检查
      checkConnectionStatus();

      // 定期检查连接状态
      statusCheckInterval = setInterval(() => {
        const isConnected = checkConnectionStatus();
        
        // 如果连接断开且不在登录页面，跳转到登录页面
        if (!isConnected && router.canGoBack()) {
          console.log('MQTT连接断开，跳转到登录页面');
          router.replace('/');
        }
      }, 5000); // 每5秒检查一次
    };

    setupConnectionMonitoring();

    return () => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
      }
    };
  }, [checkConnectionStatus]);

  return {
    ...connectionState,
    reconnect,
    checkConnectionStatus,
  };
};
